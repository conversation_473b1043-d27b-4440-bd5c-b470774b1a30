import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';

function AudioControls({ selectedDevice, frequency, isSpectrumStreaming }) {
  const [isAudioStreaming, setIsAudioStreaming] = useState(false);
  const [volume, setVolume] = useState(50);
  const [isMuted, setIsMuted] = useState(false);
  const [demodMode, setDemodMode] = useState('FM');
  const [audioError, setAudioError] = useState('');

  // Load audio state on component mount
  useEffect(() => {
    const loadAudioState = async () => {
      try {
        const state = await invoke('get_audio_state');
        setIsAudioStreaming(state.is_streaming);
        setVolume(Math.round(state.volume * 100));
        setIsMuted(state.muted);
        if (state.demod_mode) {
          setDemodMode(state.demod_mode);
        }
      } catch (e) {
        console.error("Failed to load audio state:", e);
      }
    };
    
    loadAudioState();
  }, []);

  const toggleAudioStreaming = async () => {
    try {
      if (isAudioStreaming) {
        await invoke('stop_audio_stream');
        setIsAudioStreaming(false);
        setAudioError('');
      } else {
        if (selectedDevice === null) {
          setAudioError('Please select an SDR device first');
          return;
        }
        
        const freqHz = Math.round(frequency * 1000000);
        await invoke('start_audio_stream', {
          deviceIndex: selectedDevice,
          centerFrequency: freqHz,
          demodMode: demodMode
        });
        setIsAudioStreaming(true);
        setAudioError('');
      }
    } catch (e) {
      console.error("Error toggling audio stream:", e);
      setAudioError(`Failed to ${isAudioStreaming ? 'stop' : 'start'} audio: ${e}`);
    }
  };

  const handleVolumeChange = async (newVolume) => {
    setVolume(newVolume);
    try {
      await invoke('set_audio_volume', { volume: newVolume / 100.0 });
      setAudioError('');
    } catch (e) {
      console.error("Error setting volume:", e);
      setAudioError(`Failed to set volume: ${e}`);
    }
  };

  const handleMuteToggle = async () => {
    const newMuted = !isMuted;
    setIsMuted(newMuted);
    try {
      await invoke('set_audio_mute', { muted: newMuted });
      setAudioError('');
    } catch (e) {
      console.error("Error toggling mute:", e);
      setAudioError(`Failed to ${newMuted ? 'mute' : 'unmute'}: ${e}`);
    }
  };

  const handleDemodModeChange = (newMode) => {
    setDemodMode(newMode);
    // If audio is streaming, we would need to restart it with the new mode
    // For now, just update the state - user will need to restart audio
  };

  const testAudioOutput = async () => {
    try {
      setAudioError('Testing audio output...');
      const result = await invoke('test_audio_output');
      setAudioError(`✅ ${result}`);
      setTimeout(() => setAudioError(''), 5000); // Clear message after 5 seconds
    } catch (e) {
      console.error("Error testing audio:", e);
      setAudioError(`❌ Audio test failed: ${e}`);
    }
  };

  const testAudioPipeline = async () => {
    try {
      setAudioError('Testing audio pipeline...');
      const result = await invoke('test_audio_pipeline');
      setAudioError(`✅ ${result}`);
      setTimeout(() => setAudioError(''), 5000); // Clear message after 5 seconds
    } catch (e) {
      console.error("Error testing audio pipeline:", e);
      setAudioError(`❌ Pipeline test failed: ${e}`);
    }
  };

  const testFmWithTone = async () => {
    try {
      setAudioError('Testing FM demodulation...');
      const result = await invoke('test_fm_with_tone');
      setAudioError(`✅ ${result}`);
      setTimeout(() => setAudioError(''), 5000); // Clear message after 5 seconds
    } catch (e) {
      console.error("Error testing FM:", e);
      setAudioError(`❌ FM test failed: ${e}`);
    }
  };

  return (
    <div className="audio-controls" style={{
      padding: "0.5rem",
      margin: "0.25rem 0.5rem",
      border: "1px solid #ddd",
      borderRadius: "4px",
      backgroundColor: "#f8f9fa"
    }}>
      <h3 style={{ margin: "0 0 0.5rem 0", fontSize: "1rem", color: "#333" }}>
        🔊 Audio Controls
      </h3>
      
      {/* Demodulation Mode Selection */}
      <div style={{ display: "flex", alignItems: "center", gap: "0.5rem", marginBottom: "0.5rem" }}>
        <label style={{ fontSize: "0.9rem", minWidth: "80px" }}>Mode:</label>
        <select 
          value={demodMode} 
          onChange={(e) => handleDemodModeChange(e.target.value)}
          style={{ padding: "0.25rem", flex: "1" }}
          disabled={isAudioStreaming}
        >
          <option value="FM">FM (Frequency Modulation)</option>
          <option value="AM">AM (Amplitude Modulation)</option>
          <option value="USB">USB (Upper Side Band)</option>
          <option value="LSB">LSB (Lower Side Band)</option>
        </select>
        {isAudioStreaming && (
          <span style={{ fontSize: "0.8rem", color: "#666", fontStyle: "italic" }}>
            Stop audio to change mode
          </span>
        )}
      </div>

      {/* Audio Streaming Control */}
      <div style={{ display: "flex", alignItems: "center", gap: "0.5rem", marginBottom: "0.5rem" }}>
        <button
          onClick={toggleAudioStreaming}
          disabled={selectedDevice === null && !isAudioStreaming}
          style={{
            backgroundColor: isAudioStreaming ? '#dc3545' : (selectedDevice === null ? '#6c757d' : '#28a745'),
            color: 'white',
            border: 'none',
            padding: '0.5rem 1rem',
            borderRadius: '4px',
            cursor: (selectedDevice === null && !isAudioStreaming) ? 'not-allowed' : 'pointer',
            opacity: (selectedDevice === null && !isAudioStreaming) ? 0.65 : 1,
            fontSize: '0.9rem',
            fontWeight: 'bold'
          }}
        >
          {isAudioStreaming ? '🔇 Stop Audio' : '🔊 Start Audio'}
        </button>

        <button
          onClick={async () => {
            try {
              const result = await invoke('test_audio_output');
              console.log('Audio test result:', result);
              alert('Audio test completed! You should have heard a 440Hz tone for 2 seconds.');
            } catch (error) {
              console.error('Audio test failed:', error);
              alert('Audio test failed: ' + error);
            }
          }}
          style={{
            backgroundColor: '#3b82f6',
            color: 'white',
            border: 'none',
            padding: '0.5rem 1rem',
            borderRadius: '6px',
            cursor: 'pointer',
            fontSize: '0.9rem',
            fontWeight: 'bold'
          }}
        >
          🎵 Test Audio (440Hz)
        </button>
        
        <span style={{ 
          flex: "1", 
          fontSize: "0.9rem",
          color: isAudioStreaming ? "green" : "#666",
          fontWeight: isAudioStreaming ? "bold" : "normal"
        }}>
          {isAudioStreaming 
            ? `🎵 Listening to ${frequency} MHz (${demodMode})`
            : selectedDevice !== null 
              ? "Ready to listen" 
              : "Select device to enable audio"
          }
        </span>
      </div>

      {/* Volume and Mute Controls */}
      <div style={{ display: "flex", alignItems: "center", gap: "0.5rem", marginBottom: "0.5rem" }}>
        <button
          onClick={handleMuteToggle}
          style={{
            backgroundColor: isMuted ? '#ffc107' : '#6c757d',
            color: isMuted ? 'black' : 'white',
            border: 'none',
            padding: '0.25rem 0.5rem',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '0.8rem',
            minWidth: '60px'
          }}
        >
          {isMuted ? '🔇 Muted' : '🔊 Audio'}
        </button>
        
        <label style={{ fontSize: "0.9rem", minWidth: "60px" }}>Volume:</label>
        <input
          type="range"
          min="0"
          max="100"
          value={volume}
          onChange={(e) => handleVolumeChange(parseInt(e.target.value))}
          disabled={isMuted}
          style={{ 
            flex: "1",
            opacity: isMuted ? 0.5 : 1
          }}
        />
        <span style={{ 
          minWidth: "40px", 
          fontSize: "0.9rem",
          color: isMuted ? "#999" : "#333"
        }}>
          {isMuted ? "---" : `${volume}%`}
        </span>
      </div>

      {/* Audio Test Buttons */}
      <div style={{ display: "flex", flexDirection: "column", gap: "0.5rem", marginBottom: "0.5rem" }}>
        <div style={{ display: "flex", alignItems: "center", gap: "0.5rem" }}>
          <button
            onClick={testAudioOutput}
            style={{
              backgroundColor: '#17a2b8',
              color: 'white',
              border: 'none',
              padding: '0.25rem 0.5rem',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '0.8rem'
            }}
          >
            🔊 Test Audio System
          </button>
          <span style={{ fontSize: "0.8rem", color: "#666", fontStyle: "italic" }}>
            Basic audio output test
          </span>
        </div>
        <div style={{ display: "flex", alignItems: "center", gap: "0.5rem" }}>
          <button
            onClick={testAudioPipeline}
            style={{
              backgroundColor: '#28a745',
              color: 'white',
              border: 'none',
              padding: '0.25rem 0.5rem',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '0.8rem'
            }}
          >
            🔧 Test Audio Pipeline
          </button>
          <span style={{ fontSize: "0.8rem", color: "#666", fontStyle: "italic" }}>
            Test SDR audio pipeline
          </span>
        </div>
        <div style={{ display: "flex", alignItems: "center", gap: "0.5rem" }}>
          <button
            onClick={testFmWithTone}
            style={{
              backgroundColor: '#6f42c1',
              color: 'white',
              border: 'none',
              padding: '0.25rem 0.5rem',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '0.8rem'
            }}
          >
            📻 Test FM Demod
          </button>
          <span style={{ fontSize: "0.8rem", color: "#666", fontStyle: "italic" }}>
            Test FM with synthetic 1kHz tone
          </span>
        </div>
      </div>

      {/* Audio Status/Tips */}
      <div style={{ fontSize: "0.8rem", color: "#666", fontStyle: "italic" }}>
        💡 Tips: FM for radio/aircraft, AM for aviation/broadcast, USB/LSB for amateur radio
      </div>

      {/* Error Display */}
      {audioError && (
        <div style={{ 
          color: "red", 
          fontSize: "0.8rem", 
          marginTop: "0.5rem",
          padding: "0.25rem",
          backgroundColor: "#ffe6e6",
          borderRadius: "3px",
          border: "1px solid #ffcccc"
        }}>
          ⚠️ {audioError}
        </div>
      )}
    </div>
  );
}

export default AudioControls;
