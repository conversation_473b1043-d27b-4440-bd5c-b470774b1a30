import React, { useEffect, useRef, useState } from 'react';
import Chart from 'chart.js/auto';

function SpectrumAnalyzer({ spectrumData }) {
  const [renderCount, setRenderCount] = useState(0);
  const [hoverInfo, setHoverInfo] = useState(null);
  const chartRef = useRef(null);
  const chartInstance = useRef(null);
  const mousePositionRef = useRef({ x: 0, y: 0 });
  
  // Log when component renders
  console.log(`SpectrumAnalyzer rendering (${renderCount}):`, 
    spectrumData ? {
      timestamp: spectrumData.timestamp,
      clientTimestamp: spectrumData.clientTimestamp,
      dataPoints: spectrumData.power_levels?.length || 0
    } : 'No data');
  
  // Increment render count on each render
  useEffect(() => {
    setRenderCount(prev => prev + 1);
  });

  useEffect(() => {
    console.log("SpectrumAnalyzer received new data:", spectrumData?.clientTimestamp);
    
    if (!spectrumData || !chartRef.current) {
      console.log("Missing data or chart ref");
      return;
    }

    // Make sure we have the required data
    const { frequencies, power_levels, center_frequency } = spectrumData;
    if (!frequencies || !power_levels || frequencies.length === 0 || power_levels.length === 0) {
      console.error("Invalid spectrum data:", { 
        hasFreq: !!frequencies, 
        hasPower: !!power_levels,
        freqLength: frequencies?.length || 0,
        powerLength: power_levels?.length || 0
      });
      return;
    }

    console.log("Creating/updating chart with valid data");
    
    try {
      // Destroy existing chart if it exists
      if (chartInstance.current) {
        console.log("Destroying existing chart instance");
        chartInstance.current.destroy();
      }

      // Extract data from the spectrum data
      const { frequencies, power_levels, center_frequency } = spectrumData;

      if (!frequencies || !power_levels) {
        console.error("Missing frequencies or power_levels in spectrum data");
        return;
      }

      // Format frequencies for display (MHz)
      const formattedFreqs = frequencies.map(f => (f / 1000000).toFixed(3));
      
      console.log("Creating new chart instance");
      // Create the chart
      const ctx = chartRef.current.getContext('2d');
      
      // Set chart background to black
      ctx.canvas.style.backgroundColor = 'black';
      
      // Create gradient fill
      const gradient = ctx.createLinearGradient(0, 0, 0, 300);
      gradient.addColorStop(0, 'rgba(0, 20, 80, 0.2)');    // Top color (dark blue)
      gradient.addColorStop(0.5, 'rgba(0, 100, 255, 0.5)'); // Middle color (medium blue)
      gradient.addColorStop(1, 'rgba(0, 191, 255, 0.8)');  // Bottom color (bright blue)
      
      chartInstance.current = new Chart(ctx, {
        type: 'line',
        data: {
          labels: formattedFreqs,
          datasets: [{
            label: 'Power (dB)',
            data: power_levels,
            borderColor: 'rgb(255, 255, 255)',
            borderWidth: 1.5,
            backgroundColor: gradient,
            pointRadius: 0,
            fill: 'start',
            tension: 0.3
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          animation: {
            duration: 0 // Disable animations for better performance
          },
          plugins: {
            legend: {
              display: false
            },
            title: {
              display: true,
              text: `Spectrum Analyzer - Center: ${(center_frequency / 1000000).toFixed(3)} MHz`,
              color: 'white',
              font: {
                size: 16
              }
            },
            tooltip: {
              enabled: true,
              mode: 'index',
              intersect: false,
              backgroundColor: 'rgba(0, 0, 0, 0.8)',
              titleColor: 'white',
              bodyColor: 'white',
              borderColor: 'rgba(255, 255, 255, 0.2)',
              borderWidth: 1,
              padding: 10,
              displayColors: false,
              callbacks: {
                title: function(tooltipItems) {
                  const idx = tooltipItems[0].dataIndex;
                  return `Frequency: ${formattedFreqs[idx]} MHz`;
                },
                label: function(context) {
                  return `Power: ${context.raw.toFixed(1)} dB`;
                }
              }
            }
          },
          hover: {
            mode: 'index',
            intersect: false
          },
          scales: {
            x: {
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              ticks: {
                color: 'rgba(255, 255, 255, 0.7)',
                maxRotation: 0,
                autoSkip: false,  // Disable automatic skipping
                maxTicksLimit: 10, // Limit to approximately 10 ticks
                callback: function(value, index, values) {
                  // Show labels at regular intervals across the full spectrum
                  const totalBins = 1024;
                  const numLabels = 8; // Show 8 labels across the spectrum
                  const interval = Math.floor(totalBins / numLabels);
                  
                  // Show first, last, and evenly spaced labels
                  if (index === 0 || index === totalBins - 1 || index % interval === 0) {
                    // Format the frequency in MHz with 3 decimal places
                    const freq = parseFloat(this.getLabelForValue(value));
                    return freq.toFixed(3);
                  }
                  return '';
                }
              }
            },
            y: {
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              ticks: {
                color: 'rgba(255, 255, 255, 0.7)',
                stepSize: 2, // Set increment to 2 dB
                callback: function(value) {
                  return value + ' dB';
                }
              },
              min: -20, // Change from -80 to -20
              max: 10,  // Keep max at 10
              title: {
                display: true,
                text: 'Power (dB)',
                color: 'rgba(255, 255, 255, 0.7)'
              }
            }
          }
        }
      });

      // Add this after creating the chart instance
      const canvas = chartRef.current;

      // Use these event handlers instead
      const handleMouseMove = (e) => {
        if (!chartInstance.current) return;
        
        const rect = canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        // Get the x-index based on mouse position
        const xAxis = chartInstance.current.scales.x;
        const yAxis = chartInstance.current.scales.y;
        
        // Check if mouse is within chart area
        if (x >= xAxis.left && x <= xAxis.right && y >= yAxis.top && y <= yAxis.bottom) {
          // Find the closest data point
          const index = Math.round((x - xAxis.left) / (xAxis.right - xAxis.left) * (formattedFreqs.length - 1));
          
          if (index >= 0 && index < formattedFreqs.length) {
            const frequency = formattedFreqs[index];
            const power = power_levels[index].toFixed(1);
            
            setHoverInfo({
              frequency,
              power
            });
          }
        }
      };

      const handleMouseOut = () => {
        setHoverInfo(null);
      };

      // Add event listeners directly to the canvas element
      canvas.addEventListener('mousemove', handleMouseMove);
      canvas.addEventListener('mouseout', handleMouseOut);

      // Make sure to clean up in the return function
      return () => {
        console.log("SpectrumAnalyzer cleanup running");
        if (chartInstance.current) {
          console.log("Destroying chart in cleanup");
          chartInstance.current.destroy();
        }
        
        // Remove event listeners
        canvas.removeEventListener('mousemove', handleMouseMove);
        canvas.removeEventListener('mouseout', handleMouseOut);
      };
    } catch (error) {
      console.error("Error updating chart:", error);
    }

    // Cleanup
    return () => {
      console.log("SpectrumAnalyzer cleanup running");
      if (chartInstance.current) {
        console.log("Destroying chart in cleanup");
        chartInstance.current.destroy();
      }
    };
  }, [spectrumData]);

  // Add a useEffect to continuously update hover info based on stored mouse position
  useEffect(() => {
    if (!chartInstance.current || !spectrumData) return;
    
    // Function to update hover info based on current mouse position
    const updateHoverInfo = () => {
      const { x, y } = mousePositionRef.current;
      if (x === 0 && y === 0) return; // Mouse hasn't moved over chart yet
      
      const canvas = chartRef.current;
      if (!canvas) return;
      
      const rect = canvas.getBoundingClientRect();
      const chartX = x - rect.left;
      const chartY = y - rect.top;
      
      // Get chart dimensions
      const xAxis = chartInstance.current.scales.x;
      const yAxis = chartInstance.current.scales.y;
      
      // Check if mouse is within chart area
      if (chartX >= xAxis.left && chartX <= xAxis.right && 
          chartY >= yAxis.top && chartY <= yAxis.bottom) {
        
        // Find the closest data point
        const index = Math.round((chartX - xAxis.left) / (xAxis.right - xAxis.left) * 
                                (spectrumData.frequencies.length - 1));
        
        if (index >= 0 && index < spectrumData.frequencies.length) {
          const frequency = (spectrumData.frequencies[index] / 1000000).toFixed(3);
          const power = spectrumData.power_levels[index].toFixed(1);
          
          setHoverInfo({
            frequency,
            power
          });
        }
      }
    };
    
    // Update hover info immediately
    updateHoverInfo();
    
    // Set up an interval to continuously update hover info
    // This ensures it stays updated even when chart data changes
    const intervalId = setInterval(updateHoverInfo, 100);
    
    return () => clearInterval(intervalId);
  }, [spectrumData, chartInstance.current]);

  return (
    <div 
      style={{ 
        position: 'relative',
        height: '250px', // Reduced height
        width: '100%', 
        backgroundColor: 'black', 
        padding: '5px', // Reduced padding
        borderRadius: '5px',
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)'
      }}
      onMouseMove={(e) => {
        // Store the current mouse position
        mousePositionRef.current = {
          x: e.clientX,
          y: e.clientY
        };
      }}
      onMouseLeave={() => {
        // Clear hover info and reset mouse position
        setHoverInfo(null);
        mousePositionRef.current = { x: 0, y: 0 };
      }}
    >
      <canvas ref={chartRef}></canvas>
      
      {hoverInfo && (
        <div style={{
          position: 'absolute',
          top: '10px',
          right: '10px',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          color: 'white',
          padding: '8px 12px',
          borderRadius: '4px',
          border: '1px solid rgba(255, 255, 255, 0.2)',
          fontSize: '14px',
          pointerEvents: 'none',
          zIndex: 1000
        }}>
          <div>Frequency: {hoverInfo.frequency} MHz</div>
          <div>Power: {hoverInfo.power} dB</div>
        </div>
      )}
    </div>
  );
}

export default SpectrumAnalyzer;































