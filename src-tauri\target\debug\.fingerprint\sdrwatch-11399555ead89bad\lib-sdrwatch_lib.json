{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[]", "target": 15503673899994093559, "profile": 17672942494452627365, "path": 10763286916239946207, "deps": [[92778171338278103, "ringbuf", false, 2750488016911249851], [3169989036267463843, "rtlsdr", false, 141866894697560672], [3722963349756955755, "once_cell", false, 16070340794055836169], [3935545708480822364, "tauri_plugin_opener", false, 5030460850064162605], [9689903380558560274, "serde", false, 5994951999489722273], [9727213718512686088, "crossbeam_channel", false, 6642595706633433476], [10755362358622467486, "tauri", false, 17456472382756333017], [12216227109419035626, "rustfft", false, 706483243389977507], [12319020793864570031, "num_complex", false, 890041494296008908], [14425588341765822814, "cpal", false, 128289206219464239], [15367738274754116744, "serde_json", false, 14995574703689203715], [16202601534839545094, "build_script_build", false, 12311347395007792811]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sdrwatch-11399555ead89bad\\dep-lib-sdrwatch_lib", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}