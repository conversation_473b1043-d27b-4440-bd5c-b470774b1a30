import React from "react";
import ReactDOM from "react-dom/client";
import App from "./App";
import "./styles.css";

// Try to import Tauri API
const initTauri = async () => {
  try {
    // Updated import for Tauri API v2
    const tauriAPI = await import('@tauri-apps/api/core');
    window.__TAURI_API__ = tauriAPI;
  } catch (err) {
    console.error("Failed to import Tauri API in main.jsx:", err);
  }
};

initTauri();

ReactDOM.createRoot(document.getElementById("root")).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);