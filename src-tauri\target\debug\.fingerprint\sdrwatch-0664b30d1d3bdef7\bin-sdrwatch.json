{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[]", "target": 1209384732098052433, "profile": 8731458305071235362, "path": 4942398508502643691, "deps": [[92778171338278103, "ringbuf", false, 15612194913880546374], [3169989036267463843, "rtlsdr", false, 17469603102116586980], [3722963349756955755, "once_cell", false, 12865724827523745461], [3935545708480822364, "tauri_plugin_opener", false, 8327944861591413700], [9689903380558560274, "serde", false, 1020034633644158563], [9727213718512686088, "crossbeam_channel", false, 2815966305215310834], [10755362358622467486, "tauri", false, 14760797944540255629], [12216227109419035626, "rustfft", false, 16852951129064067807], [12319020793864570031, "num_complex", false, 4470306320619880940], [14425588341765822814, "cpal", false, 2676596858995401342], [15367738274754116744, "serde_json", false, 7403869831301006401], [16202601534839545094, "sdrwatch_lib", false, 17996156027535941069], [16202601534839545094, "build_script_build", false, 2630548222888506972]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sdrwatch-0664b30d1d3bdef7\\dep-bin-sdrwatch", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}